import React, { useState } from 'react';
import { Calculator, Save, Download, ArrowRight } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const AsIsPage: React.FC = () => {
  const [policyData, setPolicyData] = useState({
    policyNumber: 'POL-2024-001',
    customerName: '<PERSON>',
    customerId: 'CUST-456789',
    policyType: 'whole-life',
    faceAmount: '500000',
    annualPremium: '25000',
    paymentPeriod: '20',
    dividendOption: 'Paid-up Additions',
    currentAge: '35',
    retirementAge: '65',
    lifeExpectancy: '85',
  });

  const [projectionSettings, setProjectionSettings] = useState({
    startYear: '2024',
    endYear: '2074',
    projectionRate: '5.5',
    inflationRate: '2.5',
    dividendRate: '6.2',
  });

  const { setActiveTab } = useDashboard();

  const policyTypes = [
    { value: 'whole-life', label: 'Whole Life Insurance' },
    { value: 'term-life', label: 'Term Life Insurance' },
    { value: 'universal-life', label: 'Universal Life Insurance' },
    { value: 'variable-life', label: 'Variable Life Insurance' },
    { value: 'indexed-universal', label: 'Indexed Universal Life' },
  ];

  const dividendOptions = [
    { value: 'Cash', label: 'Cash' },
    { value: 'Accumulate', label: 'Accumulate' },
    { value: 'Paid-up Additions', label: 'Paid-up Additions' },
    { value: 'Reduce Premium', label: 'Reduce Premium' },
  ];

  const handlePolicyDataChange = (field: string, value: string) => {
    setPolicyData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleProjectionChange = (field: string, value: string) => {
    setProjectionSettings(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSaveAsIs = () => {
    // Save AS-IS configuration
    console.log('Saving AS-IS configuration:', { policyData, projectionSettings });
    alert('AS-IS configuration saved successfully!');
  };

  const handleGenerateProjection = () => {
    // Generate AS-IS projection
    console.log('Generating AS-IS projection:', { policyData, projectionSettings });
    alert('AS-IS projection generated successfully!');
  };

  const handleProceedToWhatIf = () => {
    setActiveTab('face-amount');
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">AS-IS Configuration</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure the current policy details for baseline illustration.</p>
      </div>

      {/* Current Policy Information */}
      <Card>
        <div className="flex items-center space-x-3 mb-6">
          <Calculator className="w-6 h-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy Information</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            label="Policy Number"
            value={policyData.policyNumber}
            onChange={(e) => handlePolicyDataChange('policyNumber', e.target.value)}
            placeholder="Enter policy number"
          />
          <Input
            label="Customer Name"
            value={policyData.customerName}
            onChange={(e) => handlePolicyDataChange('customerName', e.target.value)}
            placeholder="Enter customer name"
          />
          <Input
            label="Customer ID"
            value={policyData.customerId}
            onChange={(e) => handlePolicyDataChange('customerId', e.target.value)}
            placeholder="Enter customer ID"
          />
          <Select
            label="Policy Type"
            value={policyData.policyType}
            onChange={(e) => handlePolicyDataChange('policyType', e.target.value)}
            options={policyTypes}
          />
          <Input
            label="Face Amount ($)"
            type="number"
            value={policyData.faceAmount}
            onChange={(e) => handlePolicyDataChange('faceAmount', e.target.value)}
            placeholder="Enter face amount"
          />
          <Input
            label="Annual Premium ($)"
            type="number"
            value={policyData.annualPremium}
            onChange={(e) => handlePolicyDataChange('annualPremium', e.target.value)}
            placeholder="Enter annual premium"
          />
          <Input
            label="Payment Period (Years)"
            type="number"
            value={policyData.paymentPeriod}
            onChange={(e) => handlePolicyDataChange('paymentPeriod', e.target.value)}
            placeholder="Enter payment period"
          />
          <Select
            label="Dividend Option"
            value={policyData.dividendOption}
            onChange={(e) => handlePolicyDataChange('dividendOption', e.target.value)}
            options={dividendOptions}
          />
          <Input
            label="Current Age"
            type="number"
            value={policyData.currentAge}
            onChange={(e) => handlePolicyDataChange('currentAge', e.target.value)}
            placeholder="Enter current age"
          />
          <Input
            label="Retirement Age"
            type="number"
            value={policyData.retirementAge}
            onChange={(e) => handlePolicyDataChange('retirementAge', e.target.value)}
            placeholder="Enter retirement age"
          />
          <Input
            label="Life Expectancy"
            type="number"
            value={policyData.lifeExpectancy}
            onChange={(e) => handlePolicyDataChange('lifeExpectancy', e.target.value)}
            placeholder="Enter life expectancy"
          />
        </div>
      </Card>

      {/* Projection Settings */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Projection Settings</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            label="Start Year"
            type="number"
            value={projectionSettings.startYear}
            onChange={(e) => handleProjectionChange('startYear', e.target.value)}
            placeholder="Enter start year"
          />
          <Input
            label="End Year"
            type="number"
            value={projectionSettings.endYear}
            onChange={(e) => handleProjectionChange('endYear', e.target.value)}
            placeholder="Enter end year"
          />
          <Input
            label="Projection Rate (%)"
            type="number"
            step="0.1"
            value={projectionSettings.projectionRate}
            onChange={(e) => handleProjectionChange('projectionRate', e.target.value)}
            placeholder="Enter projection rate"
          />
          <Input
            label="Inflation Rate (%)"
            type="number"
            step="0.1"
            value={projectionSettings.inflationRate}
            onChange={(e) => handleProjectionChange('inflationRate', e.target.value)}
            placeholder="Enter inflation rate"
          />
          <Input
            label="Dividend Rate (%)"
            type="number"
            step="0.1"
            value={projectionSettings.dividendRate}
            onChange={(e) => handleProjectionChange('dividendRate', e.target.value)}
            placeholder="Enter dividend rate"
          />
        </div>
      </Card>

      {/* Current Policy Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Current Policy Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Annual Premium</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              ${parseInt(policyData.annualPremium).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Face Amount</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${parseInt(policyData.faceAmount).toLocaleString()}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Payment Period</p>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {policyData.paymentPeriod} Years
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">Policy Type</p>
            <p className="text-lg font-semibold text-orange-600 dark:text-orange-400">
              {policyTypes.find(type => type.value === policyData.policyType)?.label}
            </p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 justify-center">
        <Button
          onClick={handleSaveAsIs}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Save className="w-4 h-4" />
          <span>Save AS-IS Configuration</span>
        </Button>
        <Button
          onClick={handleGenerateProjection}
          variant="secondary"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Generate AS-IS Projection</span>
        </Button>
        <Button
          onClick={handleProceedToWhatIf}
          className="flex items-center space-x-2"
        >
          <span>Proceed to What-If Analysis</span>
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default AsIsPage;
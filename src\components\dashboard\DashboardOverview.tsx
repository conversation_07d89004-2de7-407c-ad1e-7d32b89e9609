import React from 'react';
import { TrendingUp, Users, FileText, DollarSign } from 'lucide-react';
import Card from '../common/Card';

const DashboardOverview: React.FC = () => {
  // Mock data for demonstration
  const currentPolicy = {
    policyNumber: 'POL-2024-001',
    customerName: '<PERSON>',
    customerId: 'CUST-456789',
    faceAmount: 500000,
    premium: 2500,
    status: 'Active',
  };

  const metrics = [
    {
      title: 'Total Policies',
      value: '1,234',
      change: '+12%',
      trend: 'up',
      icon: FileText,
      color: 'blue',
    },
    {
      title: 'Active Customers',
      value: '856',
      change: '+8%',
      trend: 'up',
      icon: Users,
      color: 'green',
    },
    {
      title: 'Monthly Premium',
      value: '$2.1M',
      change: '+15%',
      trend: 'up',
      icon: DollarSign,
      color: 'purple',
    },
    {
      title: 'Growth Rate',
      value: '23.5%',
      change: '+5%',
      trend: 'up',
      icon: TrendingUp,
      color: 'orange',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard Overview</h1>
        <p className="text-gray-600">Welcome back! Here's your insurance portfolio summary.</p>
      </div>

      {/* Current Policy Card */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Current Policy</h3>
          <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            {currentPolicy.status}
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <p className="text-sm text-gray-600">Policy Number</p>
            <p className="text-lg font-semibold text-gray-900">{currentPolicy.policyNumber}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Customer Name</p>
            <p className="text-lg font-semibold text-gray-900">{currentPolicy.customerName}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Customer ID</p>
            <p className="text-lg font-semibold text-gray-900">{currentPolicy.customerId}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Face Amount</p>
            <p className="text-lg font-semibold text-gray-900">
              ${currentPolicy.faceAmount.toLocaleString()}
            </p>
          </div>
        </div>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => {
          const Icon = metric.icon;
          const colorClasses = {
            blue: 'bg-blue-100 text-blue-600',
            green: 'bg-green-100 text-green-600',
            purple: 'bg-purple-100 text-purple-600',
            orange: 'bg-orange-100 text-orange-600',
          };

          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                  <p className="text-sm text-green-600 mt-1">{metric.change} from last month</p>
                </div>
                <div className={`w-12 h-12 rounded-lg ${colorClasses[metric.color as keyof typeof colorClasses]} flex items-center justify-center`}>
                  <Icon className="w-6 h-6" />
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Recent Activity */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {[
            { action: 'Policy illustration created', policy: 'POL-2024-001', time: '2 hours ago' },
            { action: 'Premium payment received', policy: 'POL-2024-002', time: '4 hours ago' },
            { action: 'Policy analysis completed', policy: 'POL-2024-003', time: '1 day ago' },
            { action: 'New customer onboarded', policy: 'POL-2024-004', time: '2 days ago' },
          ].map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
              <div>
                <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                <p className="text-sm text-gray-500">{activity.policy}</p>
              </div>
              <p className="text-sm text-gray-500">{activity.time}</p>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default DashboardOverview;
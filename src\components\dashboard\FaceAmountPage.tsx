import React, { useState } from 'react';
import { Calculator, TrendingUp, Save, Download } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const FaceAmountPage: React.FC = () => {
  const [scenarios, setScenarios] = useState([
    {
      id: '1',
      name: 'Increase by 25%',
      type: 'percentage',
      value: '25',
      newFaceAmount: 625000,
      premiumImpact: 6250,
      enabled: true,
    },
    {
      id: '2',
      name: 'Decrease by 25%',
      type: 'percentage',
      value: '-25',
      newFaceAmount: 375000,
      premiumImpact: -6250,
      enabled: false,
    },
    {
      id: '3',
      name: 'Double Amount',
      type: 'multiplier',
      value: '2',
      newFaceAmount: 1000000,
      premiumImpact: 25000,
      enabled: false,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: '',
    type: 'fixed',
    value: '',
  });

  const { addScenario } = useDashboard();

  const currentFaceAmount = 500000;
  const currentPremium = 25000;

  const scenarioTypes = [
    { value: 'percentage', label: 'Percentage Change' },
    { value: 'fixed', label: 'Fixed Amount' },
    { value: 'multiplier', label: 'Multiplier' },
  ];

  const handleScenarioToggle = (id: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id ? { ...scenario, enabled: !scenario.enabled } : scenario
    ));
  };

  const calculateNewAmount = (type: string, value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return currentFaceAmount;

    switch (type) {
      case 'percentage':
        return currentFaceAmount * (1 + numValue / 100);
      case 'fixed':
        return numValue;
      case 'multiplier':
        return currentFaceAmount * numValue;
      default:
        return currentFaceAmount;
    }
  };

  const calculatePremiumImpact = (newAmount: number) => {
    const ratio = newAmount / currentFaceAmount;
    return (currentPremium * ratio) - currentPremium;
  };

  const handleCustomScenarioChange = (field: string, value: string) => {
    setCustomScenario(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomScenario = () => {
    if (!customScenario.name || !customScenario.value) {
      alert('Please fill in all fields for the custom scenario');
      return;
    }

    const newAmount = calculateNewAmount(customScenario.type, customScenario.value);
    const premiumImpact = calculatePremiumImpact(newAmount);

    const newScenario = {
      id: Date.now().toString(),
      name: customScenario.name,
      type: customScenario.type,
      value: customScenario.value,
      newFaceAmount: newAmount,
      premiumImpact,
      enabled: true,
    };

    setScenarios(prev => [...prev, newScenario]);
    setCustomScenario({ name: '', type: 'fixed', value: '' });
  };

  const saveSelectedScenarios = () => {
    const selectedScenarios = scenarios.filter(scenario => scenario.enabled);
    
    selectedScenarios.forEach(scenario => {
      const newScenario = {
        id: Date.now().toString() + Math.random(),
        name: `Face Amount: ${scenario.name}`,
        policyId: 'POL-2024-001',
        asIsDetails: 'Current Policy Details',
        whatIfOptions: [`Face Amount: ${scenario.name} (${scenario.type === 'percentage' ? scenario.value + '%' : '$' + scenario.newFaceAmount.toLocaleString()})`],
        category: 'face-amount' as const,
        data: {
          originalAmount: currentFaceAmount,
          newAmount: scenario.newFaceAmount,
          premiumImpact: scenario.premiumImpact,
          changeType: scenario.type,
          changeValue: scenario.value,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      addScenario(newScenario);
    });

    alert(`${selectedScenarios.length} Face Amount scenarios saved successfully!`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Face Amount Analysis</h1>
        <p className="text-gray-600 dark:text-gray-400">Analyze different face amount scenarios and their impact on premiums.</p>
      </div>

      {/* Current Policy Info */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy</h3>
          <Calculator className="w-6 h-6 text-green-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Face Amount</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${currentFaceAmount.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Annual Premium</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              ${currentPremium.toLocaleString()}
            </p>
          </div>
        </div>
      </Card>

      {/* Predefined Scenarios */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Face Amount Scenarios</h3>
        <div className="space-y-4">
          {scenarios.map((scenario) => (
            <div
              key={scenario.id}
              className={`p-4 border rounded-lg transition-all ${
                scenario.enabled
                  ? 'border-blue-300 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={scenario.enabled}
                    onChange={() => handleScenarioToggle(scenario.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{scenario.name}</h4>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className={`w-4 h-4 ${scenario.premiumImpact >= 0 ? 'text-red-500' : 'text-green-500'}`} />
                  <span className={`text-sm font-medium ${scenario.premiumImpact >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {scenario.premiumImpact >= 0 ? '+' : ''}${scenario.premiumImpact.toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">New Face Amount</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    ${scenario.newFaceAmount.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Change</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    {scenario.type === 'percentage' ? `${scenario.value}%` : 
                     scenario.type === 'multiplier' ? `${scenario.value}x` : 
                     `$${parseFloat(scenario.value).toLocaleString()}`}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Premium Impact</p>
                  <p className={`font-semibold ${scenario.premiumImpact >= 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {scenario.premiumImpact >= 0 ? '+' : ''}${scenario.premiumImpact.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Custom Scenario */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Create Custom Scenario</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <Input
            label="Scenario Name"
            value={customScenario.name}
            onChange={(e) => handleCustomScenarioChange('name', e.target.value)}
            placeholder="Enter scenario name"
          />
          <Select
            label="Change Type"
            value={customScenario.type}
            onChange={(e) => handleCustomScenarioChange('type', e.target.value)}
            options={scenarioTypes}
          />
          <Input
            label="Value"
            type="number"
            value={customScenario.value}
            onChange={(e) => handleCustomScenarioChange('value', e.target.value)}
            placeholder="Enter value"
          />
          <div className="flex items-end">
            <Button onClick={addCustomScenario} className="w-full">
              Add Scenario
            </Button>
          </div>
        </div>
        
        {customScenario.value && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">New Face Amount</p>
                <p className="font-semibold text-gray-900 dark:text-gray-100">
                  ${calculateNewAmount(customScenario.type, customScenario.value).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Premium Impact</p>
                <p className="font-semibold text-blue-600 dark:text-blue-400">
                  ${calculatePremiumImpact(calculateNewAmount(customScenario.type, customScenario.value)).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={saveSelectedScenarios}
          className="flex items-center space-x-2"
          disabled={!scenarios.some(s => s.enabled)}
        >
          <Save className="w-4 h-4" />
          <span>Save Selected Scenarios</span>
        </Button>
        <Button
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Export Analysis</span>
        </Button>
      </div>
    </div>
  );
};

export default FaceAmountPage;
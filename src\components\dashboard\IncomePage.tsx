import React, { useState } from 'react';
import { Wallet, TrendingDown, Save, Download } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const IncomePage: React.FC = () => {
  const [scenarios, setScenarios] = useState([
    {
      id: '1',
      name: 'Annual Withdrawal $5,000',
      type: 'withdrawal',
      amount: '5000',
      frequency: 'annual',
      startAge: '65',
      endAge: '85',
      cashValueImpact: -100000,
      enabled: true,
    },
    {
      id: '2',
      name: 'Policy Loan $25,000',
      type: 'loan',
      amount: '25000',
      frequency: 'one-time',
      interestRate: '5.5',
      repaymentPlan: 'interest-only',
      cashValueImpact: -25000,
      enabled: false,
    },
    {
      id: '3',
      name: 'Systematic Withdrawal Plan',
      type: 'systematic',
      amount: '2000',
      frequency: 'monthly',
      startAge: '60',
      endAge: '80',
      cashValueImpact: -480000,
      enabled: false,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: '',
    type: 'withdrawal',
    amount: '',
    frequency: 'annual',
    startAge: '65',
    endAge: '85',
    interestRate: '5.5',
    repaymentPlan: 'interest-only',
  });

  const { addScenario } = useDashboard();

  const currentCashValue = 75000;
  const currentAge = 45;

  const incomeTypes = [
    { value: 'withdrawal', label: 'Cash Withdrawal' },
    { value: 'loan', label: 'Policy Loan' },
    { value: 'systematic', label: 'Systematic Withdrawal' },
    { value: 'income-replacement', label: 'Income Replacement' },
  ];

  const frequencies = [
    { value: 'one-time', label: 'One Time' },
    { value: 'annual', label: 'Annual' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
  ];

  const repaymentPlans = [
    { value: 'interest-only', label: 'Interest Only' },
    { value: 'principal-interest', label: 'Principal + Interest' },
    { value: 'deferred', label: 'Deferred Repayment' },
    { value: 'automatic', label: 'Automatic Repayment' },
  ];

  const handleScenarioToggle = (id: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id ? { ...scenario, enabled: !scenario.enabled } : scenario
    ));
  };

  const calculateCashValueImpact = (type: string, amount: string, frequency: string, duration: number) => {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return 0;

    let totalWithdrawal = 0;
    switch (frequency) {
      case 'one-time':
        totalWithdrawal = numAmount;
        break;
      case 'annual':
        totalWithdrawal = numAmount * duration;
        break;
      case 'monthly':
        totalWithdrawal = numAmount * 12 * duration;
        break;
      case 'quarterly':
        totalWithdrawal = numAmount * 4 * duration;
        break;
    }

    // For loans, impact is immediate loan amount
    if (type === 'loan') {
      return -numAmount;
    }

    return -totalWithdrawal;
  };

  const handleCustomScenarioChange = (field: string, value: string) => {
    setCustomScenario(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomScenario = () => {
    if (!customScenario.name || !customScenario.amount) {
      alert('Please fill in all required fields for the custom scenario');
      return;
    }

    const duration = parseInt(customScenario.endAge) - parseInt(customScenario.startAge);
    const cashValueImpact = calculateCashValueImpact(
      customScenario.type,
      customScenario.amount,
      customScenario.frequency,
      duration
    );

    const newScenario = {
      id: Date.now().toString(),
      name: customScenario.name,
      type: customScenario.type,
      amount: customScenario.amount,
      frequency: customScenario.frequency,
      startAge: customScenario.startAge,
      endAge: customScenario.endAge,
      interestRate: customScenario.interestRate,
      repaymentPlan: customScenario.repaymentPlan,
      cashValueImpact,
      enabled: true,
    };

    setScenarios(prev => [...prev, newScenario]);
    setCustomScenario({
      name: '',
      type: 'withdrawal',
      amount: '',
      frequency: 'annual',
      startAge: '65',
      endAge: '85',
      interestRate: '5.5',
      repaymentPlan: 'interest-only',
    });
  };

  const saveSelectedScenarios = () => {
    const selectedScenarios = scenarios.filter(scenario => scenario.enabled);
    
    selectedScenarios.forEach(scenario => {
      const newScenario = {
        id: Date.now().toString() + Math.random(),
        name: `Income: ${scenario.name}`,
        policyId: 'POL-2024-001',
        asIsDetails: 'Current Policy Details',
        whatIfOptions: [`Income: ${scenario.name} (${scenario.type} - $${parseFloat(scenario.amount).toLocaleString()} ${scenario.frequency})`],
        category: 'income' as const,
        data: {
          incomeType: scenario.type,
          amount: parseFloat(scenario.amount),
          frequency: scenario.frequency,
          startAge: scenario.startAge,
          endAge: scenario.endAge,
          cashValueImpact: scenario.cashValueImpact,
          interestRate: scenario.interestRate,
          repaymentPlan: scenario.repaymentPlan,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      addScenario(newScenario);
    });

    alert(`${selectedScenarios.length} Income scenarios saved successfully!`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Income Analysis (Loan & Withdrawal)</h1>
        <p className="text-gray-600 dark:text-gray-400">Analyze different income strategies using policy loans and withdrawals.</p>
      </div>

      {/* Current Policy Info */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy</h3>
          <Wallet className="w-6 h-6 text-purple-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Cash Value</p>
            <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              ${currentCashValue.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Age</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {currentAge} years
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Available Loan Value</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${(currentCashValue * 0.9).toLocaleString()}
            </p>
          </div>
        </div>
      </Card>

      {/* Predefined Scenarios */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Income Scenarios</h3>
        <div className="space-y-4">
          {scenarios.map((scenario) => (
            <div
              key={scenario.id}
              className={`p-4 border rounded-lg transition-all ${
                scenario.enabled
                  ? 'border-purple-300 bg-purple-50 dark:bg-purple-900/20 dark:border-purple-700'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={scenario.enabled}
                    onChange={() => handleScenarioToggle(scenario.id)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{scenario.name}</h4>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingDown className="w-4 h-4 text-red-500" />
                  <span className="text-sm font-medium text-red-600">
                    ${Math.abs(scenario.cashValueImpact).toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Type</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                    {scenario.type}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Amount</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    ${parseFloat(scenario.amount).toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Frequency</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                    {scenario.frequency}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Duration</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    {scenario.startAge} - {scenario.endAge} years
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Custom Scenario */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Create Custom Income Scenario</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Scenario Name"
              value={customScenario.name}
              onChange={(e) => handleCustomScenarioChange('name', e.target.value)}
              placeholder="Enter scenario name"
            />
            <Select
              label="Income Type"
              value={customScenario.type}
              onChange={(e) => handleCustomScenarioChange('type', e.target.value)}
              options={incomeTypes}
            />
            <Input
              label="Amount ($)"
              type="number"
              value={customScenario.amount}
              onChange={(e) => handleCustomScenarioChange('amount', e.target.value)}
              placeholder="Enter amount"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Frequency"
              value={customScenario.frequency}
              onChange={(e) => handleCustomScenarioChange('frequency', e.target.value)}
              options={frequencies}
            />
            <Input
              label="Start Age"
              type="number"
              value={customScenario.startAge}
              onChange={(e) => handleCustomScenarioChange('startAge', e.target.value)}
              placeholder="Start age"
            />
            <Input
              label="End Age"
              type="number"
              value={customScenario.endAge}
              onChange={(e) => handleCustomScenarioChange('endAge', e.target.value)}
              placeholder="End age"
            />
            <div className="flex items-end">
              <Button onClick={addCustomScenario} className="w-full">
                Add Scenario
              </Button>
            </div>
          </div>

          {customScenario.type === 'loan' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Interest Rate (%)"
                type="number"
                step="0.1"
                value={customScenario.interestRate}
                onChange={(e) => handleCustomScenarioChange('interestRate', e.target.value)}
                placeholder="Interest rate"
              />
              <Select
                label="Repayment Plan"
                value={customScenario.repaymentPlan}
                onChange={(e) => handleCustomScenarioChange('repaymentPlan', e.target.value)}
                options={repaymentPlans}
              />
            </div>
          )}
        </div>
        
        {customScenario.amount && customScenario.startAge && customScenario.endAge && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg mt-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">Total Impact</p>
                <p className="font-semibold text-red-600">
                  ${Math.abs(calculateCashValueImpact(
                    customScenario.type,
                    customScenario.amount,
                    customScenario.frequency,
                    parseInt(customScenario.endAge) - parseInt(customScenario.startAge)
                  )).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Duration</p>
                <p className="font-semibold text-gray-900 dark:text-gray-100">
                  {parseInt(customScenario.endAge) - parseInt(customScenario.startAge)} years
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Income Strategy Information */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Income Strategy Options</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100">Policy Loans</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">Tax-free access to cash value with interest charges</p>
          </div>
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100">Cash Withdrawals</h4>
            <p className="text-sm text-green-700 dark:text-green-300 mt-1">Direct withdrawal from cash value (may be taxable)</p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={saveSelectedScenarios}
          className="flex items-center space-x-2"
          disabled={!scenarios.some(s => s.enabled)}
        >
          <Save className="w-4 h-4" />
          <span>Save Selected Scenarios</span>
        </Button>
        <Button
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Export Analysis</span>
        </Button>
      </div>
    </div>
  );
};

export default IncomePage;
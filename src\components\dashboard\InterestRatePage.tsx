import React, { useState } from 'react';
import { Percent, TrendingUp, TrendingDown, Save, Download } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const InterestRatePage: React.FC = () => {
  const [scenarios, setScenarios] = useState([
    {
      id: '1',
      name: 'Current Rate Scenario',
      type: 'current',
      rate: '5.5',
      duration: 'lifetime',
      cashValueImpact: 0,
      deathBenefitImpact: 0,
      enabled: true,
    },
    {
      id: '2',
      name: 'Guaranteed Rate Only',
      type: 'guaranteed',
      rate: '3.0',
      duration: 'lifetime',
      cashValueImpact: -150000,
      deathBenefitImpact: -75000,
      enabled: false,
    },
    {
      id: '3',
      name: 'Stress Test - Low Rate',
      type: 'stress-low',
      rate: '2.0',
      duration: 'lifetime',
      cashValueImpact: -250000,
      deathBenefitImpact: -125000,
      enabled: false,
    },
    {
      id: '4',
      name: 'Stress Test - High Rate',
      type: 'stress-high',
      rate: '8.0',
      duration: 'lifetime',
      cashValueImpact: 200000,
      deathBenefitImpact: 100000,
      enabled: false,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: '',
    type: 'custom',
    rate: '',
    duration: 'lifetime',
    startYear: '2024',
    endYear: '2074',
  });

  const { addScenario } = useDashboard();

  const currentRate = 5.5;
  const guaranteedRate = 3.0;
  const currentCashValue = 75000;
  const currentDeathBenefit = 500000;

  const rateTypes = [
    { value: 'current', label: 'Current Rate' },
    { value: 'guaranteed', label: 'Guaranteed Rate' },
    { value: 'stress-low', label: 'Stress Test - Low' },
    { value: 'stress-high', label: 'Stress Test - High' },
    { value: 'custom', label: 'Custom Rate' },
  ];

  const durations = [
    { value: 'lifetime', label: 'Lifetime' },
    { value: '10-years', label: '10 Years' },
    { value: '20-years', label: '20 Years' },
    { value: 'custom', label: 'Custom Period' },
  ];

  const handleScenarioToggle = (id: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id ? { ...scenario, enabled: !scenario.enabled } : scenario
    ));
  };

  const calculateImpact = (rate: string, duration: string) => {
    const numRate = parseFloat(rate);
    if (isNaN(numRate)) return { cashValue: 0, deathBenefit: 0 };

    const rateDiff = numRate - currentRate;
    const multiplier = duration === 'lifetime' ? 50 : duration === '20-years' ? 30 : 20;
    
    const cashValueImpact = rateDiff * multiplier * 1000;
    const deathBenefitImpact = cashValueImpact * 0.5;

    return {
      cashValue: cashValueImpact,
      deathBenefit: deathBenefitImpact,
    };
  };

  const handleCustomScenarioChange = (field: string, value: string) => {
    setCustomScenario(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomScenario = () => {
    if (!customScenario.name || !customScenario.rate) {
      alert('Please fill in all required fields for the custom scenario');
      return;
    }

    const impact = calculateImpact(customScenario.rate, customScenario.duration);

    const newScenario = {
      id: Date.now().toString(),
      name: customScenario.name,
      type: customScenario.type,
      rate: customScenario.rate,
      duration: customScenario.duration,
      cashValueImpact: impact.cashValue,
      deathBenefitImpact: impact.deathBenefit,
      enabled: true,
    };

    setScenarios(prev => [...prev, newScenario]);
    setCustomScenario({
      name: '',
      type: 'custom',
      rate: '',
      duration: 'lifetime',
      startYear: '2024',
      endYear: '2074',
    });
  };

  const saveSelectedScenarios = () => {
    const selectedScenarios = scenarios.filter(scenario => scenario.enabled);
    
    selectedScenarios.forEach(scenario => {
      const newScenario = {
        id: Date.now().toString() + Math.random(),
        name: `Interest Rate: ${scenario.name}`,
        policyId: 'POL-2024-001',
        asIsDetails: 'Current Policy Details',
        whatIfOptions: [`Interest Rate: ${scenario.name} (${scenario.rate}% - ${scenario.duration})`],
        category: 'interest-rate' as const,
        data: {
          rateType: scenario.type,
          interestRate: parseFloat(scenario.rate),
          duration: scenario.duration,
          cashValueImpact: scenario.cashValueImpact,
          deathBenefitImpact: scenario.deathBenefitImpact,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      addScenario(newScenario);
    });

    alert(`${selectedScenarios.length} Interest Rate scenarios saved successfully!`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Interest Rate Based Analysis</h1>
        <p className="text-gray-600 dark:text-gray-400">Analyze policy performance under different interest rate scenarios.</p>
      </div>

      {/* Current Rate Info */}
      <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-indigo-200 dark:border-indigo-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Interest Rates</h3>
          <Percent className="w-6 h-6 text-indigo-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Crediting Rate</p>
            <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
              {currentRate}%
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Guaranteed Rate</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              {guaranteedRate}%
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Rate Spread</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {(currentRate - guaranteedRate).toFixed(1)}%
            </p>
          </div>
        </div>
      </Card>

      {/* Predefined Scenarios */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Interest Rate Scenarios</h3>
        <div className="space-y-4">
          {scenarios.map((scenario) => (
            <div
              key={scenario.id}
              className={`p-4 border rounded-lg transition-all ${
                scenario.enabled
                  ? 'border-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 dark:border-indigo-700'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={scenario.enabled}
                    onChange={() => handleScenarioToggle(scenario.id)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{scenario.name}</h4>
                </div>
                <div className="flex items-center space-x-2">
                  {scenario.cashValueImpact >= 0 ? (
                    <TrendingUp className="w-4 h-4 text-green-500" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-500" />
                  )}
                  <span className={`text-sm font-medium ${scenario.cashValueImpact >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.cashValueImpact >= 0 ? '+' : ''}${scenario.cashValueImpact.toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Interest Rate</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    {scenario.rate}%
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Duration</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                    {scenario.duration.replace('-', ' ')}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Cash Value Impact</p>
                  <p className={`font-semibold ${scenario.cashValueImpact >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.cashValueImpact >= 0 ? '+' : ''}${scenario.cashValueImpact.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Death Benefit Impact</p>
                  <p className={`font-semibold ${scenario.deathBenefitImpact >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.deathBenefitImpact >= 0 ? '+' : ''}${scenario.deathBenefitImpact.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Custom Scenario */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Create Custom Interest Rate Scenario</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Scenario Name"
              value={customScenario.name}
              onChange={(e) => handleCustomScenarioChange('name', e.target.value)}
              placeholder="Enter scenario name"
            />
            <Input
              label="Interest Rate (%)"
              type="number"
              step="0.1"
              value={customScenario.rate}
              onChange={(e) => handleCustomScenarioChange('rate', e.target.value)}
              placeholder="Enter interest rate"
            />
            <Select
              label="Duration"
              value={customScenario.duration}
              onChange={(e) => handleCustomScenarioChange('duration', e.target.value)}
              options={durations}
            />
          </div>
          
          {customScenario.duration === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Start Year"
                type="number"
                value={customScenario.startYear}
                onChange={(e) => handleCustomScenarioChange('startYear', e.target.value)}
                placeholder="Start year"
              />
              <Input
                label="End Year"
                type="number"
                value={customScenario.endYear}
                onChange={(e) => handleCustomScenarioChange('endYear', e.target.value)}
                placeholder="End year"
              />
            </div>
          )}
          
          <div className="flex justify-end">
            <Button onClick={addCustomScenario}>
              Add Scenario
            </Button>
          </div>
        </div>
        
        {customScenario.rate && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg mt-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">Cash Value Impact</p>
                <p className={`font-semibold ${calculateImpact(customScenario.rate, customScenario.duration).cashValue >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {calculateImpact(customScenario.rate, customScenario.duration).cashValue >= 0 ? '+' : ''}${calculateImpact(customScenario.rate, customScenario.duration).cashValue.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Death Benefit Impact</p>
                <p className={`font-semibold ${calculateImpact(customScenario.rate, customScenario.duration).deathBenefit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {calculateImpact(customScenario.rate, customScenario.duration).deathBenefit >= 0 ? '+' : ''}${calculateImpact(customScenario.rate, customScenario.duration).deathBenefit.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Interest Rate Environment */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Interest Rate Environment</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <h4 className="font-medium text-red-900 dark:text-red-100">Low Rate Environment</h4>
            <p className="text-sm text-red-700 dark:text-red-300 mt-1">Reduced cash value growth and policy performance</p>
          </div>
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Current Rate Environment</h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">Balanced performance based on current market conditions</p>
          </div>
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100">High Rate Environment</h4>
            <p className="text-sm text-green-700 dark:text-green-300 mt-1">Enhanced cash value growth and policy benefits</p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={saveSelectedScenarios}
          className="flex items-center space-x-2"
          disabled={!scenarios.some(s => s.enabled)}
        >
          <Save className="w-4 h-4" />
          <span>Save Selected Scenarios</span>
        </Button>
        <Button
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Export Analysis</span>
        </Button>
      </div>
    </div>
  );
};

export default InterestRatePage;
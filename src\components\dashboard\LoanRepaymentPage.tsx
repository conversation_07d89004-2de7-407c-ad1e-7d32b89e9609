import React, { useState } from 'react';
import { CreditCard, TrendingUp, Save, Download } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const LoanRepaymentPage: React.FC = () => {
  const [scenarios, setScenarios] = useState([
    {
      id: '1',
      name: 'Full Loan Repayment',
      type: 'full',
      amount: '50000',
      schedule: 'immediate',
      years: '1',
      interestSavings: 15000,
      cashValueImpact: 50000,
      enabled: true,
    },
    {
      id: '2',
      name: 'Partial Repayment (50%)',
      type: 'partial',
      amount: '25000',
      schedule: 'immediate',
      years: '1',
      interestSavings: 7500,
      cashValueImpact: 25000,
      enabled: false,
    },
    {
      id: '3',
      name: 'Interest-Only Payments',
      type: 'interest-only',
      amount: '2750',
      schedule: 'annual',
      years: '10',
      interestSavings: 0,
      cashValueImpact: 27500,
      enabled: false,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: '',
    type: 'partial',
    amount: '',
    schedule: 'immediate',
    years: '1',
    interestRate: '5.5',
  });

  const { addScenario } = useDashboard();

  const currentLoanBalance = 50000;
  const currentInterestRate = 5.5;
  const currentCashValue = 75000;

  const repaymentTypes = [
    { value: 'full', label: 'Full Repayment' },
    { value: 'partial', label: 'Partial Repayment' },
    { value: 'interest-only', label: 'Interest-Only Payments' },
    { value: 'deferred', label: 'Deferred Repayment' },
  ];

  const scheduleOptions = [
    { value: 'immediate', label: 'Immediate' },
    { value: 'annual', label: 'Annual Payments' },
    { value: 'monthly', label: 'Monthly Payments' },
    { value: 'quarterly', label: 'Quarterly Payments' },
  ];

  const handleScenarioToggle = (id: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id ? { ...scenario, enabled: !scenario.enabled } : scenario
    ));
  };

  const calculateInterestSavings = (type: string, amount: string, years: string) => {
    const numAmount = parseFloat(amount);
    const numYears = parseFloat(years);
    
    if (isNaN(numAmount) || isNaN(numYears)) return 0;

    switch (type) {
      case 'full':
        return currentLoanBalance * (currentInterestRate / 100) * numYears;
      case 'partial':
        return numAmount * (currentInterestRate / 100) * numYears;
      case 'interest-only':
        return 0; // No principal reduction
      case 'deferred':
        return -(currentLoanBalance * (currentInterestRate / 100) * numYears); // Additional interest
      default:
        return 0;
    }
  };

  const calculateCashValueImpact = (type: string, amount: string, schedule: string, years: string) => {
    const numAmount = parseFloat(amount);
    const numYears = parseFloat(years);
    
    if (isNaN(numAmount)) return 0;

    switch (type) {
      case 'full':
      case 'partial':
        return schedule === 'immediate' ? -numAmount : -(numAmount / numYears);
      case 'interest-only':
        return -(numAmount * numYears);
      case 'deferred':
        return 0; // No immediate impact
      default:
        return 0;
    }
  };

  const handleCustomScenarioChange = (field: string, value: string) => {
    setCustomScenario(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomScenario = () => {
    if (!customScenario.name || !customScenario.amount) {
      alert('Please fill in all required fields for the custom scenario');
      return;
    }

    const interestSavings = calculateInterestSavings(
      customScenario.type,
      customScenario.amount,
      customScenario.years
    );
    
    const cashValueImpact = calculateCashValueImpact(
      customScenario.type,
      customScenario.amount,
      customScenario.schedule,
      customScenario.years
    );

    const newScenario = {
      id: Date.now().toString(),
      name: customScenario.name,
      type: customScenario.type,
      amount: customScenario.amount,
      schedule: customScenario.schedule,
      years: customScenario.years,
      interestSavings,
      cashValueImpact,
      enabled: true,
    };

    setScenarios(prev => [...prev, newScenario]);
    setCustomScenario({
      name: '',
      type: 'partial',
      amount: '',
      schedule: 'immediate',
      years: '1',
      interestRate: '5.5',
    });
  };

  const saveSelectedScenarios = () => {
    const selectedScenarios = scenarios.filter(scenario => scenario.enabled);
    
    selectedScenarios.forEach(scenario => {
      const newScenario = {
        id: Date.now().toString() + Math.random(),
        name: `Loan Repayment: ${scenario.name}`,
        policyId: 'POL-2024-001',
        asIsDetails: 'Current Policy Details',
        whatIfOptions: [`Loan Repayment: ${scenario.name} ($${parseFloat(scenario.amount).toLocaleString()} - ${scenario.schedule})`],
        category: 'loan-repayment' as const,
        data: {
          repaymentType: scenario.type,
          amount: parseFloat(scenario.amount),
          schedule: scenario.schedule,
          years: parseFloat(scenario.years),
          interestSavings: scenario.interestSavings,
          cashValueImpact: scenario.cashValueImpact,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      addScenario(newScenario);
    });

    alert(`${selectedScenarios.length} Loan Repayment scenarios saved successfully!`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Loan Repayment Analysis</h1>
        <p className="text-gray-600 dark:text-gray-400">Analyze different loan repayment strategies and their impact on policy performance.</p>
      </div>

      {/* Current Loan Info */}
      <Card className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border-orange-200 dark:border-orange-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Loan Status</h3>
          <CreditCard className="w-6 h-6 text-orange-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Outstanding Loan Balance</p>
            <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
              ${currentLoanBalance.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Interest Rate</p>
            <p className="text-2xl font-bold text-red-600 dark:text-red-400">
              {currentInterestRate}%
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Available Cash Value</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${currentCashValue.toLocaleString()}
            </p>
          </div>
        </div>
      </Card>

      {/* Predefined Scenarios */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Loan Repayment Scenarios</h3>
        <div className="space-y-4">
          {scenarios.map((scenario) => (
            <div
              key={scenario.id}
              className={`p-4 border rounded-lg transition-all ${
                scenario.enabled
                  ? 'border-orange-300 bg-orange-50 dark:bg-orange-900/20 dark:border-orange-700'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={scenario.enabled}
                    onChange={() => handleScenarioToggle(scenario.id)}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{scenario.name}</h4>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className={`w-4 h-4 ${scenario.interestSavings >= 0 ? 'text-green-500' : 'text-red-500'}`} />
                  <span className={`text-sm font-medium ${scenario.interestSavings >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.interestSavings >= 0 ? 'Save ' : 'Cost '}${Math.abs(scenario.interestSavings).toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Repayment Amount</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    ${parseFloat(scenario.amount).toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Schedule</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100 capitalize">
                    {scenario.schedule}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Duration</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    {scenario.years} year{parseFloat(scenario.years) !== 1 ? 's' : ''}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Cash Value Impact</p>
                  <p className={`font-semibold ${scenario.cashValueImpact >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.cashValueImpact >= 0 ? '+' : ''}${scenario.cashValueImpact.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Custom Scenario */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Create Custom Repayment Scenario</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Scenario Name"
              value={customScenario.name}
              onChange={(e) => handleCustomScenarioChange('name', e.target.value)}
              placeholder="Enter scenario name"
            />
            <Select
              label="Repayment Type"
              value={customScenario.type}
              onChange={(e) => handleCustomScenarioChange('type', e.target.value)}
              options={repaymentTypes}
            />
            <Input
              label="Amount ($)"
              type="number"
              value={customScenario.amount}
              onChange={(e) => handleCustomScenarioChange('amount', e.target.value)}
              placeholder="Enter repayment amount"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Payment Schedule"
              value={customScenario.schedule}
              onChange={(e) => handleCustomScenarioChange('schedule', e.target.value)}
              options={scheduleOptions}
            />
            <Input
              label="Duration (Years)"
              type="number"
              value={customScenario.years}
              onChange={(e) => handleCustomScenarioChange('years', e.target.value)}
              placeholder="Duration in years"
            />
            <Input
              label="Interest Rate (%)"
              type="number"
              step="0.1"
              value={customScenario.interestRate}
              onChange={(e) => handleCustomScenarioChange('interestRate', e.target.value)}
              placeholder="Interest rate"
            />
            <div className="flex items-end">
              <Button onClick={addCustomScenario} className="w-full">
                Add Scenario
              </Button>
            </div>
          </div>
        </div>
        
        {customScenario.amount && customScenario.years && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg mt-4">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">Interest Savings</p>
                <p className="font-semibold text-green-600">
                  ${calculateInterestSavings(customScenario.type, customScenario.amount, customScenario.years).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Cash Value Impact</p>
                <p className="font-semibold text-red-600">
                  ${Math.abs(calculateCashValueImpact(customScenario.type, customScenario.amount, customScenario.schedule, customScenario.years)).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Repayment Strategy Information */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Repayment Strategy Benefits</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100">Full Repayment</h4>
            <p className="text-sm text-green-700 dark:text-green-300 mt-1">Eliminates interest charges and restores full death benefit</p>
          </div>
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100">Partial Repayment</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">Reduces interest burden while maintaining some loan flexibility</p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={saveSelectedScenarios}
          className="flex items-center space-x-2"
          disabled={!scenarios.some(s => s.enabled)}
        >
          <Save className="w-4 h-4" />
          <span>Save Selected Scenarios</span>
        </Button>
        <Button
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Export Analysis</span>
        </Button>
      </div>
    </div>
  );
};

export default LoanRepaymentPage;
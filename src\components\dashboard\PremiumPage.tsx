import React, { useState } from 'react';
import { DollarSign, TrendingUp, Save, Download } from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { useDashboard } from '../../contexts/DashboardContext';

const PremiumPage: React.FC = () => {
  const [scenarios, setScenarios] = useState([
    {
      id: '1',
      name: 'Increase Premium by 10%',
      type: 'percentage',
      value: '10',
      newPremium: 27500,
      cashValueImpact: 2500,
      enabled: true,
    },
    {
      id: '2',
      name: 'Decrease Premium by 10%',
      type: 'percentage',
      value: '-10',
      newPremium: 22500,
      cashValueImpact: -2500,
      enabled: false,
    },
    {
      id: '3',
      name: 'Skip Premium Payments',
      type: 'skip',
      value: '0',
      newPremium: 0,
      cashValueImpact: -25000,
      enabled: false,
    },
  ]);

  const [customScenario, setCustomScenario] = useState({
    name: '',
    type: 'percentage',
    value: '',
    duration: '1',
  });

  const { addScenario } = useDashboard();

  const currentPremium = 25000;
  const currentCashValue = 15000;

  const scenarioTypes = [
    { value: 'percentage', label: 'Percentage Change' },
    { value: 'fixed', label: 'Fixed Amount' },
    { value: 'skip', label: 'Skip Payments' },
    { value: 'solve', label: 'Premium Solve' },
  ];

  const handleScenarioToggle = (id: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === id ? { ...scenario, enabled: !scenario.enabled } : scenario
    ));
  };

  const calculateNewPremium = (type: string, value: string) => {
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return currentPremium;

    switch (type) {
      case 'percentage':
        return currentPremium * (1 + numValue / 100);
      case 'fixed':
        return numValue;
      case 'skip':
        return 0;
      case 'solve':
        return numValue; // Target premium for solve
      default:
        return currentPremium;
    }
  };

  const calculateCashValueImpact = (newPremium: number) => {
    const premiumDiff = newPremium - currentPremium;
    return premiumDiff * 0.1; // Simplified calculation
  };

  const handleCustomScenarioChange = (field: string, value: string) => {
    setCustomScenario(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const addCustomScenario = () => {
    if (!customScenario.name || !customScenario.value) {
      alert('Please fill in all fields for the custom scenario');
      return;
    }

    const newPremium = calculateNewPremium(customScenario.type, customScenario.value);
    const cashValueImpact = calculateCashValueImpact(newPremium);

    const newScenario = {
      id: Date.now().toString(),
      name: customScenario.name,
      type: customScenario.type,
      value: customScenario.value,
      newPremium,
      cashValueImpact,
      enabled: true,
    };

    setScenarios(prev => [...prev, newScenario]);
    setCustomScenario({ name: '', type: 'percentage', value: '', duration: '1' });
  };

  const saveSelectedScenarios = () => {
    const selectedScenarios = scenarios.filter(scenario => scenario.enabled);
    
    selectedScenarios.forEach(scenario => {
      const newScenario = {
        id: Date.now().toString() + Math.random(),
        name: `Premium: ${scenario.name}`,
        policyId: 'POL-2024-001',
        asIsDetails: 'Current Policy Details',
        whatIfOptions: [`Premium: ${scenario.name} (${scenario.type === 'percentage' ? scenario.value + '%' : '$' + scenario.newPremium.toLocaleString()})`],
        category: 'premium' as const,
        data: {
          originalPremium: currentPremium,
          newPremium: scenario.newPremium,
          cashValueImpact: scenario.cashValueImpact,
          changeType: scenario.type,
          changeValue: scenario.value,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      addScenario(newScenario);
    });

    alert(`${selectedScenarios.length} Premium scenarios saved successfully!`);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Premium Analysis</h1>
        <p className="text-gray-600 dark:text-gray-400">Analyze different premium payment scenarios and their impact on cash value.</p>
      </div>

      {/* Current Policy Info */}
      <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200 dark:border-blue-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy</h3>
          <DollarSign className="w-6 h-6 text-blue-600" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Annual Premium</p>
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              ${currentPremium.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Current Cash Value</p>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">
              ${currentCashValue.toLocaleString()}
            </p>
          </div>
        </div>
      </Card>

      {/* Predefined Scenarios */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Premium Scenarios</h3>
        <div className="space-y-4">
          {scenarios.map((scenario) => (
            <div
              key={scenario.id}
              className={`p-4 border rounded-lg transition-all ${
                scenario.enabled
                  ? 'border-blue-300 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700'
                  : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={scenario.enabled}
                    onChange={() => handleScenarioToggle(scenario.id)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <h4 className="font-medium text-gray-900 dark:text-gray-100">{scenario.name}</h4>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className={`w-4 h-4 ${scenario.cashValueImpact >= 0 ? 'text-green-500' : 'text-red-500'}`} />
                  <span className={`text-sm font-medium ${scenario.cashValueImpact >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.cashValueImpact >= 0 ? '+' : ''}${scenario.cashValueImpact.toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <p className="text-gray-600 dark:text-gray-400">New Premium</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    ${scenario.newPremium.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Change</p>
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    {scenario.type === 'percentage' ? `${scenario.value}%` : 
                     scenario.type === 'skip' ? 'Skip Payments' : 
                     `$${parseFloat(scenario.value).toLocaleString()}`}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600 dark:text-gray-400">Cash Value Impact</p>
                  <p className={`font-semibold ${scenario.cashValueImpact >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {scenario.cashValueImpact >= 0 ? '+' : ''}${scenario.cashValueImpact.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Custom Scenario */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">Create Custom Premium Scenario</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
          <Input
            label="Scenario Name"
            value={customScenario.name}
            onChange={(e) => handleCustomScenarioChange('name', e.target.value)}
            placeholder="Enter scenario name"
          />
          <Select
            label="Change Type"
            value={customScenario.type}
            onChange={(e) => handleCustomScenarioChange('type', e.target.value)}
            options={scenarioTypes}
          />
          <Input
            label="Value"
            type="number"
            value={customScenario.value}
            onChange={(e) => handleCustomScenarioChange('value', e.target.value)}
            placeholder="Enter value"
          />
          <Input
            label="Duration (Years)"
            type="number"
            value={customScenario.duration}
            onChange={(e) => handleCustomScenarioChange('duration', e.target.value)}
            placeholder="Duration"
          />
          <div className="flex items-end">
            <Button onClick={addCustomScenario} className="w-full">
              Add Scenario
            </Button>
          </div>
        </div>
        
        {customScenario.value && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Preview</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">New Premium</p>
                <p className="font-semibold text-gray-900 dark:text-gray-100">
                  ${calculateNewPremium(customScenario.type, customScenario.value).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Cash Value Impact</p>
                <p className="font-semibold text-blue-600 dark:text-blue-400">
                  ${calculateCashValueImpact(calculateNewPremium(customScenario.type, customScenario.value)).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Premium Payment Schedule */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Premium Payment Options</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100">Level Premium</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">Consistent premium payments throughout policy life</p>
          </div>
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100">Flexible Premium</h4>
            <p className="text-sm text-green-700 dark:text-green-300 mt-1">Adjust premium payments based on cash flow</p>
          </div>
          <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <h4 className="font-medium text-purple-900 dark:text-purple-100">Single Premium</h4>
            <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">One-time lump sum premium payment</p>
          </div>
        </div>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button
          onClick={saveSelectedScenarios}
          className="flex items-center space-x-2"
          disabled={!scenarios.some(s => s.enabled)}
        >
          <Save className="w-4 h-4" />
          <span>Save Selected Scenarios</span>
        </Button>
        <Button
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Export Analysis</span>
        </Button>
      </div>
    </div>
  );
};

export default PremiumPage;
import React, { createContext, useContext, useReducer } from 'react';
import { Policy, Scenario, DashboardState } from '../types';

interface DashboardContextType extends DashboardState {
  setActiveTab: (tab: string) => void;
  setCurrentPolicy: (policy: Policy | null) => void;
  addScenario: (scenario: Scenario) => void;
  updateScenario: (id: string, updates: Partial<Scenario>) => void;
  deleteScenario: (id: string) => void;
  deleteMultipleScenarios: (ids: string[]) => void;
  toggleScenarioSelection: (id: string) => void;
  selectAllScenarios: (select: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

const initialState: DashboardState = {
  activeTab: 'dashboard',
  currentPolicy: null,
  scenarios: [],
  selectedScenarios: [],
  loading: false,
  error: null,
};

type DashboardAction = 
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'SET_CURRENT_POLICY'; payload: Policy | null }
  | { type: 'ADD_SCENARIO'; payload: Scenario }
  | { type: 'UPDATE_SCENARIO'; payload: { id: string; updates: Partial<Scenario> } }
  | { type: 'DELETE_SCENARIO'; payload: string }
  | { type: 'DELETE_MULTIPLE_SCENARIOS'; payload: string[] }
  | { type: 'TOGGLE_SCENARIO_SELECTION'; payload: string }
  | { type: 'SELECT_ALL_SCENARIOS'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

const dashboardReducer = (state: DashboardState, action: DashboardAction): DashboardState => {
  switch (action.type) {
    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload };
    case 'SET_CURRENT_POLICY':
      return { ...state, currentPolicy: action.payload };
    case 'ADD_SCENARIO':
      return { ...state, scenarios: [...state.scenarios, action.payload] };
    case 'UPDATE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.map(scenario =>
          scenario.id === action.payload.id
            ? { ...scenario, ...action.payload.updates }
            : scenario
        ),
      };
    case 'DELETE_SCENARIO':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => scenario.id !== action.payload),
        selectedScenarios: state.selectedScenarios.filter(id => id !== action.payload),
      };
    case 'DELETE_MULTIPLE_SCENARIOS':
      return {
        ...state,
        scenarios: state.scenarios.filter(scenario => !action.payload.includes(scenario.id)),
        selectedScenarios: [],
      };
    case 'TOGGLE_SCENARIO_SELECTION':
      return {
        ...state,
        selectedScenarios: state.selectedScenarios.includes(action.payload)
          ? state.selectedScenarios.filter(id => id !== action.payload)
          : [...state.selectedScenarios, action.payload],
      };
    case 'SELECT_ALL_SCENARIOS':
      return {
        ...state,
        selectedScenarios: action.payload ? state.scenarios.map(s => s.id) : [],
      };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
};

export const DashboardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);

  return (
    <DashboardContext.Provider value={{
      ...state,
      setActiveTab: (tab: string) => dispatch({ type: 'SET_ACTIVE_TAB', payload: tab }),
      setCurrentPolicy: (policy: Policy | null) => dispatch({ type: 'SET_CURRENT_POLICY', payload: policy }),
      addScenario: (scenario: Scenario) => dispatch({ type: 'ADD_SCENARIO', payload: scenario }),
      updateScenario: (id: string, updates: Partial<Scenario>) => dispatch({ type: 'UPDATE_SCENARIO', payload: { id, updates } }),
      deleteScenario: (id: string) => dispatch({ type: 'DELETE_SCENARIO', payload: id }),
      deleteMultipleScenarios: (ids: string[]) => dispatch({ type: 'DELETE_MULTIPLE_SCENARIOS', payload: ids }),
      toggleScenarioSelection: (id: string) => dispatch({ type: 'TOGGLE_SCENARIO_SELECTION', payload: id }),
      selectAllScenarios: (select: boolean) => dispatch({ type: 'SELECT_ALL_SCENARIOS', payload: select }),
      setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
      setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
    }}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = (): DashboardContextType => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
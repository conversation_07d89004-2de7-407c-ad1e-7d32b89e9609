export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
}

export interface Policy {
  id: string;
  policyNumber: string;
  customerName: string;
  customerId: string;
  policyType: string;
  status: 'active' | 'inactive' | 'pending';
  faceAmount: number;
  premium: number;
  createdAt: Date;
}

export interface Scenario {
  id: string;
  name: string;
  policyId: string;
  asIsDetails: string;
  whatIfOptions: string[];
  category: 'face-amount' | 'premium' | 'income' | 'loan-repayment' | 'interest-rate' | 'policy-lapse';
  data: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  rememberMe: boolean;
}

export interface DashboardState {
  activeTab: string;
  currentPolicy: Policy | null;
  scenarios: Scenario[];
  selectedScenarios: string[];
  loading: boolean;
  error: string | null;
}